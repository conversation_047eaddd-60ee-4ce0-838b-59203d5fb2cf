"use client";

import { deleteChat } from "@/actions/chat-actions";
import { useRouter } from "next/navigation";
import { Trash } from "lucide-react";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@radix-ui/react-dropdown-menu";
import { SidebarGroupAction } from "../ui/sidebar";

export function ClearChats() {
  const router = useRouter();

  const handleClearAll = async () => {
    try {
      await deleteChat("all");
      router.refresh(); // Refresh the page to reflect changes
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarGroupAction>
          <Trash />
        </SidebarGroupAction>
      </DropdownMenuTrigger>
      <DropdownMenuContent side="right" align="start">
        <DropdownMenuItem onClick={() => handleClearAll()}>
          <span>Clear all Chats</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}