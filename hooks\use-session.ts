// src/hooks/use-session.ts
"use client";

import { useEffect, useState } from "react";
import { getSession } from "next-auth/react";

// Cache the session promise at the module level
let cachedSessionPromise: Promise<any> | null = null;

export function useSession() {
  const [session, setSession] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSession = async () => {
      try {
        if (!cachedSessionPromise) {
          cachedSessionPromise = getSession();
        }
        const sessionData = await cachedSessionPromise;
        setSession(sessionData);
      } catch (error) {
        console.error("Error fetching session:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSession();
  }, []);

  return { session, isLoading };
}