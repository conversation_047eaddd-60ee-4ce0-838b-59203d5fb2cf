"use client";

export interface SSEMessage {
  type: string;
  data: any;
  timestamp: string;
}

export interface SSEOptions {
  onMessage?: (message: SSEMessage) => void;
  onError?: (error: Error) => void;
  onOpen?: () => void;
  onClose?: () => void;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export class SSEClient {
  private url: string;
  private options: SSEOptions;
  private abortController: AbortController | null = null;
  private reconnectAttempts = 0;
  private isConnected = false;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  constructor(url: string, options: SSEOptions = {}) {
    this.url = url;
    this.options = {
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      ...options,
    };
  }

  async connect(requestBody?: any): Promise<void> {
    if (this.isConnected) {
      return;
    }

    this.abortController = new AbortController();

    try {
      const response = await fetch(this.url, {
        method: requestBody ? 'POST' : 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
        body: requestBody ? JSON.stringify(requestBody) : undefined,
        signal: this.abortController.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('No response body');
      }

      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.options.onOpen?.();

      await this.processStream(response.body);

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Connection was intentionally aborted
        return;
      }

      this.isConnected = false;
      this.options.onError?.(error instanceof Error ? error : new Error('Unknown error'));
      
      // Attempt to reconnect if within limits
      if (this.reconnectAttempts < (this.options.maxReconnectAttempts || 5)) {
        this.scheduleReconnect(requestBody);
      }
    }
  }

  private async processStream(body: ReadableStream<Uint8Array>): Promise<void> {
    const reader = body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (this.isConnected) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        
        // Keep the last incomplete line in the buffer
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              const message: SSEMessage = {
                type: data.type || 'message',
                data: data,
                timestamp: data.timestamp || new Date().toISOString(),
              };
              
              this.options.onMessage?.(message);
              
              // Check if this is a completion or error message
              if (data.type === 'complete' || data.type === 'error') {
                this.disconnect();
                return;
              }
            } catch (parseError) {
              console.error('Error parsing SSE message:', parseError);
            }
          } else if (line === '') {
            // Empty line indicates end of message
            continue;
          }
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        this.options.onError?.(error);
      }
    } finally {
      reader.releaseLock();
      this.isConnected = false;
      this.options.onClose?.();
    }
  }

  private scheduleReconnect(requestBody?: any): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.reconnectAttempts++;
    const delay = this.options.reconnectInterval! * Math.pow(2, this.reconnectAttempts - 1);

    this.reconnectTimeout = setTimeout(() => {
      if (!this.isConnected) {
        this.connect(requestBody);
      }
    }, delay);
  }

  disconnect(): void {
    this.isConnected = false;
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }
}

// Utility function for simple SSE requests
export async function streamRequest(
  url: string,
  requestBody: any,
  options: SSEOptions = {}
): Promise<SSEClient> {
  const client = new SSEClient(url, options);
  await client.connect(requestBody);
  return client;
}
