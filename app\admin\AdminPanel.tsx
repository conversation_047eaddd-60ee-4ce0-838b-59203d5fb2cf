// app/admin/page.tsx
'use client'

import { useState, useEffect } from 'react'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import { User, UserRole } from '@prisma/client'

export default function AdminPage() {
    const [users, setUsers] = useState<User[]>([])
    const [selectedUser, setSelectedUser] = useState<User | null>(null)
    const [editData, setEditData] = useState<Partial<User>>({})
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {

        const fetchUsers = async () => {
            try {
                const response = await fetch('/api/admin/users')
                const data = await response.json()
                setUsers(data)
            } catch {
                toast.error('Failed to load users')
            } finally {
                setIsLoading(false)
            }
        }

        fetchUsers()
    }, [])

    const handleEditUser = async () => {
        if (!selectedUser) return

        try {
            const response = await fetch(`/api/admin/users/${selectedUser.id}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(editData),
            })

            if (response.ok) {
                setUsers(users.map(u => u.id === selectedUser.id ? { ...u, ...editData } : u))
                toast.success('User updated successfully')
            }
        } catch {
            toast.error('Failed to update user')
        }
    }

    const handlePasswordReset = async (userId: string) => {
        try {
            const response = await fetch(`/api/admin/users/${userId}/reset-password`, {
                method: 'POST',
            })

            if (response.ok) {
                toast.success('Password reset email sent')
            }
        } catch {
            toast.error('Failed to reset password')
        }
    }

    const handleDeleteUser = async (userId: string) => {
        try {
            const response = await fetch(`/api/admin/users/${userId}`, {
                method: 'DELETE',
            })

            if (response.ok) {
                setUsers(users.filter(u => u.id !== userId))
                toast.success('User deleted successfully')
            }
        } catch {
            toast.error('Failed to delete user')
        }
    }

    if (isLoading) return <div>Loading...</div>

    return (
        <div className="container mx-auto p-4">
            <h1 className="text-2xl font-bold mb-4">User Management</h1>

            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Email</TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Created At</TableHead>
                        <TableHead>Actions</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {users.map((user) => (
                        <TableRow key={user.id}>
                            <TableCell>{user.email}</TableCell>
                            <TableCell>{user.name || '-'}</TableCell>
                            <TableCell>{user.role}</TableCell>
                            <TableCell>
                                {new Date(user.createdAt).toLocaleDateString()}
                            </TableCell>
                            <TableCell className="space-x-2">
                                <Dialog>
                                    <DialogTrigger asChild>
                                        <Button
                                            variant="outline"
                                            onClick={() => {
                                                setSelectedUser(user)
                                                setEditData(user)
                                            }}
                                        >
                                            Edit
                                        </Button>
                                    </DialogTrigger>

                                    <Button
                                        variant="outline"
                                        onClick={() => handlePasswordReset(user.id)}
                                    >
                                        Reset Password
                                    </Button>

                                    <Button
                                        variant="destructive"
                                        onClick={() => handleDeleteUser(user.id)}
                                    >
                                        Delete
                                    </Button>
                                </Dialog>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>

            <Dialog open={!!selectedUser} onOpenChange={() => setSelectedUser(null)}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit User</DialogTitle>
                    </DialogHeader>

                    <div className="space-y-4">
                        <div>
                            <Label>Email</Label>
                            <Input
                                value={editData.email || ''}
                                onChange={(e) => setEditData({ ...editData, email: e.target.value })}
                            />
                        </div>

                        <div>
                            <Label>Role</Label>
                            <Select
                                value={editData.role || 'USER'}
                                onValueChange={(value) => setEditData({ ...editData, role: value as UserRole })}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select role" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="USER">User</SelectItem>
                                    <SelectItem value="ADMIN">Admin</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <Button onClick={handleEditUser}>Save Changes</Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    )
}