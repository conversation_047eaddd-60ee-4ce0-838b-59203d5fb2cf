"use client";
import { AIInputWithLoading } from "@/components/ui/ai-input-with-loading";
import { MessageContainer } from "@/components/ui/message-container";
import React, { useState } from "react";
import { useSession } from "@/hooks/use-session";
import { useChatContext } from "@/contexts/ChatContext";
import { useStreamingChat } from "@/hooks/use-streaming-chat";
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import logo from '@/assets/chattylogo.svg';

function ChatPage() {
  const { session } = useSession();
  const { state } = useChatContext();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pendingMessage, setPendingMessage] = useState<string | null>(null);

  const { sendMessage } = useStreamingChat({
    onStreamStart: (chatId) => {
      console.log('Stream started for chat:', chatId);
      // Clear pending message since it's now in the chat
      setPendingMessage(null);
      // Add a small delay to ensure smooth transition
      setTimeout(() => {
        // Navigate to the new chat without page refresh
        router.push(`/chat/${chatId}`);
      }, 100);
    },
    onStreamChunk: (content) => {
      // Content is automatically handled by the chat context
      console.log('Received chunk:', content);
    },
    onStreamComplete: (fullContent) => {
      console.log('Stream completed:', fullContent);
      setIsSubmitting(false);
      setPendingMessage(null);
    },
    onStreamError: (error) => {
      console.error('Streaming error:', error);
      setIsSubmitting(false);
      setPendingMessage(null);
    }
  });

  const handleMessageSubmit = async (message: string) => {
    if (!session.user?.email) {
      console.error("User not authenticated");
      return;
    }

    if (isStreaming || isSubmitting) {
      return; // Prevent multiple submissions
    }

    // Set pending message immediately for optimistic UI update
    setPendingMessage(message);
    setIsSubmitting(true);

    try {
      const result = await sendMessage(message, undefined, session.user.email);

      if (!result.success) {
        console.error("Message submission failed:", result.error);
        setIsSubmitting(false);
        setPendingMessage(null);
      }
      // Success handling is done in the streaming callbacks
    } catch (error) {
      console.error("Message submission failed:", error);
      setIsSubmitting(false);
      setPendingMessage(null);
    }
  };

  // Get streaming state from context
  const isStreaming = state.isStreaming;
  const streamingMessageId = state.streamingMessageId;

  // Show welcome screen when no current chat and no pending message (regardless of other chats)
  const showWelcome = !state.currentChatId && !pendingMessage;
  const currentMessages = state.currentChatId ? state.messages[state.currentChatId] || [] : [];

  // Create temporary messages array including pending message for immediate display
  const displayMessages = [...currentMessages];
  if (pendingMessage && !state.currentChatId) {
    displayMessages.push({
      id: 'pending-user-message',
      content: pendingMessage,
      role: 'user' as const,
      sender: {
        id: session?.user?.email || 'user',
        role: 'USER' as const,
        email: session?.user?.email
      },
      createdAt: new Date()
    });

    // Add placeholder AI message
    displayMessages.push({
      id: 'pending-ai-message',
      content: '',
      role: 'assistant' as const,
      sender: { id: 'AI', role: 'ADMIN' as const },
      createdAt: new Date()
    });
  }

  return (
    <div className="flex flex-col h-full">
      {showWelcome && !isSubmitting && !isStreaming && (
        <div className="flex flex-col items-center space-y-4 justify-center flex-1">
          <Image
            src={logo}
            width={500}
            height={500}
            alt="Chatty Logo"
          />
          <h1 className="text-[2rem]">Start a new Chat</h1>
          <h1 className="text-[1.5rem] pb-2 text-gray-400">Chat with our AI</h1>
        </div>
      )}

      {(displayMessages.length > 0 || pendingMessage) && (
        <MessageContainer
          messages={displayMessages}
          streamingMessageId={pendingMessage ? 'pending-ai-message' : streamingMessageId}
          showTimestamps={false}
          className="flex-1 min-h-0"
        />
      )}

      <div className="flex-shrink-0 p-4 border-t bg-background/80 backdrop-blur-sm sticky bottom-0">
        <div className="max-w-3xl mx-auto">
          <AIInputWithLoading
            onSubmit={handleMessageSubmit}
            loadingDuration={3000}
            placeholder="Type a message..."
            disabled={isSubmitting || isStreaming}
          />
        </div>
      </div>
    </div>
  );
}

export default ChatPage;