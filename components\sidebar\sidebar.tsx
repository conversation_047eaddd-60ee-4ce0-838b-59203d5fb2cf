import {
  <PERSON><PERSON>ronUp,
  MessageCircle,
  Plus,
} from "lucide-react";
import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import SignOutBtn from "@/components/sidebar/signout-btn";
import { ChatActions } from "@/components/chat/ChatActions";
// import { ClearChats } from "@/components/chat/ClearChats";
import prisma from "@/prisma";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Prisma } from "@prisma/client";

// Detailed type definitions matching Prisma-generated types
const chatInclude = {
  shares: {
    include: {
      sender: true
    }
  },
  user: true
} satisfies Prisma.ChatInclude;

const shareInclude = {
  chat: {
    include: {
      user: true,
      shares: {
        include: {
          sender: true
        }
      }
    }
  },
  sender: true
} satisfies Prisma.ShareInclude;

type PrismaUser = Prisma.UserGetPayload<{
  select: {
    id: true,
    name: true,
    email: true,
    role: true,
    image: true
  }
}>;

type PrismaShare = Prisma.ShareGetPayload<{
  include: typeof shareInclude
}>;

type SharedChat = {
  type: 'shared';
  id: string;
  title: string;
  userId: string;
  createdAt: Date;
  shares: PrismaShare[];
  user: PrismaUser;
  sharedBy: PrismaUser;
  sharedAt: Date;
}

type OwnedChat = {
  type: 'owned';
  id: string;
  title: string;
  userId: string;
  createdAt: Date;
  shares: PrismaShare[];
  user: PrismaUser;
  sharedWith: string[];
}

type CombinedChat = SharedChat | OwnedChat;

interface Session {
  user: PrismaUser & { id: string };
}

interface AppSidebarProps {
  session: Session | null;
}

// Helper function to get user chats
async function getUserChatsDirect(userId: string): Promise<CombinedChat[]> {
  const ownedChats = await prisma.chat.findMany({
    where: { userId },
    include: chatInclude,
    orderBy: { createdAt: 'desc' },
  });

  const sharedChats = await prisma.share.findMany({
    where: { recipientId: userId },
    include: shareInclude,
    orderBy: { createdAt: 'desc' }
  });

  // Explicitly map to CombinedChat type
  const mappedOwnedChats: OwnedChat[] = ownedChats.map(chat => ({
    type: 'owned',
    id: chat.id,
    title: chat.title || '',
    userId: chat.userId,
    createdAt: chat.createdAt,
    shares: chat.shares as PrismaShare[],
    user: chat.user,
    sharedWith: chat.shares.map(share => share.recipientId)
  }));

  const mappedSharedChats: SharedChat[] = sharedChats.map(share => ({
    type: 'shared',
    id: share.chat.id,
    title: share.chat.title || '',
    userId: share.chat.userId,
    createdAt: share.chat.createdAt,
    shares: share.chat.shares as PrismaShare[],
    user: share.chat.user,
    sharedBy: share.sender,
    sharedAt: share.createdAt
  }));

  return [...mappedOwnedChats, ...mappedSharedChats];
}

export async function AppSidebar({ session }: AppSidebarProps) {
  if (!session) {
    return null;
  }

  const chats = await getUserChatsDirect(session.user.id);

  // Import the client-side sidebar component
  const { ClientSidebar } = await import('./client-sidebar');

  return <ClientSidebar session={session} initialChats={chats} />;
}