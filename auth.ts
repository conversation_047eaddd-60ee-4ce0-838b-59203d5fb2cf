import NextAuth from "next-auth";
import type { NextAuthConfig } from "next-auth";
import prisma from "./prisma";
import credentials from "next-auth/providers/credentials";
import { LoginSchema } from "./schemas";
import bcrypt from "bcryptjs";

 
export const { handlers, signIn, signOut, auth } = NextAuth({
    session: { strategy: "jwt" },
    callbacks: {
      jwt({ token, user }) {
        if (user) { // User is available during sign-in
          token.id = user.id
          token.role = user.role
        }
        return token
      },
      session({ session, token }) {
        session.user.id = token.id as string
        session.user.role = token.role as string
        return session
      },
    },
    providers: [
      credentials({
        async authorize(credentials) {
          const validatedFields = LoginSchema.safeParse(credentials);
  
          if (validatedFields.success) {
            const { email, password } = validatedFields.data;
  
            const user = await prisma.user.findUnique({ where: { email } });
  
            if (!user || !user.password) return null;
  
            const passwordsMatch = await bcrypt.compare(password, user.password);
            if (passwordsMatch) return user;
          }
  
          return null;
        },
      }),
    ],
})