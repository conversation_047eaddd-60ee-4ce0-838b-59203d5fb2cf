"use client";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { toast } from "sonner";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"


const Account = () => {
  const { data: session, update } = useSession();
  const [formData, setFormData] = useState({
    id: "",
    name: "",
    email: "",
    image: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form data with session values
  useEffect(() => {
    if (session?.user) {
      setFormData({
        id: session.user.id || "",
        name: session.user.name || "",
        email: session.user.email || "",
        image: session.user.image || "",
      });
    }
    console.log(session);
  }, [session]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch("/api/account", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Failed to update account");
      }

      // Update session with new data
      await update({
        ...session,
        user: {
          ...session?.user,
          ...formData
        }
      });

      toast.success("Account updated successfully");
    } catch {
      toast.error("Update failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  if (!session?.user) {
    return <div>Loading...</div>;
  }

  return (
    <Card className="w-[350px]">
      <CardHeader>
        <CardTitle>Account Overview</CardTitle>
        <CardDescription>Update your account information</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="grid w-full items-center gap-4">
            <div className="flex flex-col space-y-1.5 justify-center items-center w-full">
              <Avatar className="w-32 h-32 rounded-[50%] object-cover">
                <AvatarImage src={formData.image} />
                <AvatarFallback>{formData.name.substring(0,2).toUpperCase()}</AvatarFallback>
              </Avatar>
              <Input
                name="image"
                placeholder="Image URL"
                value={formData.image}
                onChange={handleChange}
                className="mt-2"
              />
            </div>
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="name">ID</Label>
              <Input
                name="name"
                value={formData.id}
                disabled
              />
            </div>
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="name">Name</Label>
              <Input
                name="name"
                value={formData.name}
                onChange={handleChange}
              />
            </div>
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="email">Email</Label>
              <Input
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link href="/chat">
          <Button variant="outline">Go Back</Button>
        </Link>
        <Button
          onClick={handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? "Saving..." : "Save Changes"}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default Account;