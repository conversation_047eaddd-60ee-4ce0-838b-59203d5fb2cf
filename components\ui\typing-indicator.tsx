"use client";

import React from 'react';
import { cn } from '@/lib/utils';

interface TypingIndicatorProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'dots' | 'pulse' | 'wave';
}

export function TypingIndicator({ 
  className, 
  size = 'md', 
  variant = 'dots' 
}: TypingIndicatorProps) {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  const containerSizeClasses = {
    sm: 'gap-1 p-2',
    md: 'gap-1.5 p-3',
    lg: 'gap-2 p-4'
  };

  if (variant === 'dots') {
    return (
      <div className={cn(
        "flex items-center justify-center bg-blue-500/10 rounded-lg w-max",
        containerSizeClasses[size],
        className
      )}>
        <div className="flex items-center space-x-1">
          <div 
            className={cn(
              "bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",
              sizeClasses[size]
            )}
            style={{ animationDelay: '0ms', animationDuration: '1.4s' }}
          />
          <div 
            className={cn(
              "bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",
              sizeClasses[size]
            )}
            style={{ animationDelay: '160ms', animationDuration: '1.4s' }}
          />
          <div 
            className={cn(
              "bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",
              sizeClasses[size]
            )}
            style={{ animationDelay: '320ms', animationDuration: '1.4s' }}
          />
        </div>
      </div>
    );
  }

  if (variant === 'pulse') {
    return (
      <div className={cn(
        "flex items-center justify-center bg-blue-500/10 rounded-lg w-max",
        containerSizeClasses[size],
        className
      )}>
        <div className={cn(
          "bg-blue-500 rounded-full animate-pulse",
          sizeClasses[size]
        )} />
      </div>
    );
  }

  if (variant === 'wave') {
    return (
      <div className={cn(
        "flex items-center justify-center bg-blue-500/10 rounded-lg w-max",
        containerSizeClasses[size],
        className
      )}>
        <div className="flex items-end space-x-1">
          {[0, 1, 2, 3, 4].map((i) => (
            <div
              key={i}
              className={cn(
                "bg-blue-500 rounded-sm animate-pulse",
                size === 'sm' ? 'w-1' : size === 'md' ? 'w-1.5' : 'w-2'
              )}
              style={{
                height: size === 'sm' ? '8px' : size === 'md' ? '12px' : '16px',
                animationDelay: `${i * 100}ms`,
                animationDuration: '1s'
              }}
            />
          ))}
        </div>
      </div>
    );
  }

  return null;
}

// Typing indicator with text
export function TypingIndicatorWithText({ 
  text = "AI is typing...", 
  className,
  size = 'md'
}: TypingIndicatorProps & { text?: string }) {
  return (
    <div className={cn("flex items-center space-x-3", className)}>
      <TypingIndicator size={size} variant="dots" />
      <span className="text-sm text-gray-500 dark:text-gray-400 animate-pulse">
        {text}
      </span>
    </div>
  );
}
