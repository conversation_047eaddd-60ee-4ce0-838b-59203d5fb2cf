import { auth } from '@/auth';
import prisma from '@/prisma';
import { NextResponse } from 'next/server';

export async function PUT(req: Request) {
  const session = await auth();
  
  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const data = await req.json();
    
    const updatedUser = await prisma.user.update({
      where: { email: session.user.email },
      data: {
        name: data.name,
        email: data.email,
        image: data.image
      }
    });

    return NextResponse.json(updatedUser);
  } catch {
    return NextResponse.json(
      { error: 'Failed to update account' },
      { status: 500 }
    );
  }
}