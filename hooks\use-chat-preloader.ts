"use client";

import { useEffect, useCallback, useRef } from 'react';
import { useChatContext } from '@/contexts/ChatContext';
import { CombinedChat } from '@/types';

interface PreloadOptions {
  preloadOnHover?: boolean;
  preloadAdjacent?: boolean;
  preloadRecent?: boolean;
  maxPreloadCount?: number;
  hoverDelay?: number;
}

const DEFAULT_OPTIONS: PreloadOptions = {
  preloadOnHover: true,
  preloadAdjacent: true,
  preloadRecent: true,
  maxPreloadCount: 5,
  hoverDelay: 300,
};

export function useChatPreloader(options: PreloadOptions = {}) {
  const { state, preloadChat } = useChatContext();
  const config = { ...DEFAULT_OPTIONS, ...options };
  const hoverTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const preloadedChats = useRef<Set<string>>(new Set());

  // Preload recent chats on mount
  useEffect(() => {
    if (config.preloadRecent && state.chats.length > 0) {
      const recentChats = state.chats
        .slice(0, config.maxPreloadCount)
        .filter(chat => !state.messages[chat.id]);

      recentChats.forEach(chat => {
        if (!preloadedChats.current.has(chat.id)) {
          preloadedChats.current.add(chat.id);
          preloadChat(chat.id).catch(console.error);
        }
      });
    }
  }, [state.chats, config.preloadRecent, config.maxPreloadCount, preloadChat, state.messages]);

  // Preload adjacent chats when current chat changes
  useEffect(() => {
    if (config.preloadAdjacent && state.currentChatId && state.chats.length > 0) {
      const currentIndex = state.chats.findIndex(chat => chat.id === state.currentChatId);
      if (currentIndex !== -1) {
        const adjacentChats: CombinedChat[] = [];
        
        // Previous chat
        if (currentIndex > 0) {
          adjacentChats.push(state.chats[currentIndex - 1]);
        }
        
        // Next chat
        if (currentIndex < state.chats.length - 1) {
          adjacentChats.push(state.chats[currentIndex + 1]);
        }

        adjacentChats.forEach(chat => {
          if (!state.messages[chat.id] && !preloadedChats.current.has(chat.id)) {
            preloadedChats.current.add(chat.id);
            preloadChat(chat.id).catch(console.error);
          }
        });
      }
    }
  }, [state.currentChatId, state.chats, config.preloadAdjacent, preloadChat, state.messages]);

  // Handle hover preloading
  const handleChatHover = useCallback((chatId: string) => {
    if (!config.preloadOnHover || state.messages[chatId] || preloadedChats.current.has(chatId)) {
      return;
    }

    // Clear any existing timeout for this chat
    const existingTimeout = hoverTimeouts.current.get(chatId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout for preloading
    const timeout = setTimeout(() => {
      if (!preloadedChats.current.has(chatId)) {
        preloadedChats.current.add(chatId);
        preloadChat(chatId).catch(console.error);
      }
      hoverTimeouts.current.delete(chatId);
    }, config.hoverDelay);

    hoverTimeouts.current.set(chatId, timeout);
  }, [config.preloadOnHover, config.hoverDelay, preloadChat, state.messages]);

  const handleChatHoverEnd = useCallback((chatId: string) => {
    const timeout = hoverTimeouts.current.get(chatId);
    if (timeout) {
      clearTimeout(timeout);
      hoverTimeouts.current.delete(chatId);
    }
  }, []);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      hoverTimeouts.current.forEach(timeout => clearTimeout(timeout));
      hoverTimeouts.current.clear();
    };
  }, []);

  // Preload specific chat manually
  const preloadSpecificChat = useCallback(async (chatId: string) => {
    if (!preloadedChats.current.has(chatId)) {
      preloadedChats.current.add(chatId);
      await preloadChat(chatId);
    }
  }, [preloadChat]);

  // Check if chat is preloaded
  const isChatPreloaded = useCallback((chatId: string) => {
    return !!state.messages[chatId];
  }, [state.messages]);

  // Get preload status
  const getPreloadStatus = useCallback(() => {
    const totalChats = state.chats.length;
    const preloadedCount = state.chats.filter(chat => state.messages[chat.id]).length;
    const loadingCount = state.loadingChats.size;

    return {
      totalChats,
      preloadedCount,
      loadingCount,
      preloadPercentage: totalChats > 0 ? (preloadedCount / totalChats) * 100 : 0,
    };
  }, [state.chats, state.messages, state.loadingChats]);

  return {
    handleChatHover,
    handleChatHoverEnd,
    preloadSpecificChat,
    isChatPreloaded,
    getPreloadStatus,
    isLoading: state.loadingChats.size > 0,
    error: state.error,
  };
}

// Hook for intersection observer-based preloading
export function useIntersectionPreloader() {
  const { preloadChat } = useChatContext();
  const observerRef = useRef<IntersectionObserver | null>(null);
  const observedElements = useRef<Map<Element, string>>(new Map());

  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const chatId = observedElements.current.get(entry.target);
            if (chatId) {
              preloadChat(chatId).catch(console.error);
            }
          }
        });
      },
      {
        rootMargin: '100px', // Start preloading when element is 100px away from viewport
        threshold: 0.1,
      }
    );

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [preloadChat]);

  const observeElement = useCallback((element: Element, chatId: string) => {
    if (observerRef.current) {
      observedElements.current.set(element, chatId);
      observerRef.current.observe(element);
    }
  }, []);

  const unobserveElement = useCallback((element: Element) => {
    if (observerRef.current) {
      observedElements.current.delete(element);
      observerRef.current.unobserve(element);
    }
  }, []);

  return {
    observeElement,
    unobserveElement,
  };
}
