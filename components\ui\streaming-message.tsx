"use client";

import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { TypingIndicator } from './typing-indicator';

interface StreamingMessageProps {
  content: string;
  isStreaming: boolean;
  role: 'user' | 'assistant';
  sender?: {
    id: string;
    role: 'USER' | 'ADMIN';
    name?: string;
    email?: string;
  };
  className?: string;
  animationSpeed?: number; // milliseconds per character
  showCursor?: boolean;
}

export function StreamingMessage({
  content,
  isStreaming,
  role,
  sender,
  className,
  animationSpeed = 20,
  showCursor = true
}: StreamingMessageProps) {
  const [displayedContent, setDisplayedContent] = useState('');
  const [showTypingCursor, setShowTypingCursor] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();

  // Progressive text rendering effect
  useEffect(() => {
    if (isStreaming && content) {
      let currentIndex = displayedContent.length;
      
      const animateText = () => {
        if (currentIndex < content.length) {
          setDisplayedContent(content.slice(0, currentIndex + 1));
          currentIndex++;
          animationRef.current = setTimeout(animateText, animationSpeed);
        } else {
          setShowTypingCursor(false);
        }
      };

      if (currentIndex < content.length) {
        setShowTypingCursor(true);
        animationRef.current = setTimeout(animateText, animationSpeed);
      }

      return () => {
        if (animationRef.current) {
          clearTimeout(animationRef.current);
        }
      };
    } else {
      // If not streaming, show full content immediately
      setDisplayedContent(content);
      setShowTypingCursor(false);
    }
  }, [content, isStreaming, animationSpeed, displayedContent.length]);

  // Auto-scroll to bottom when content updates
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
  }, [displayedContent]);

  // Clean up animation on unmount
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    };
  }, []);

  const isUser = role === 'user' || sender?.role === 'USER';
  const isAI = role === 'assistant' || sender?.role === 'ADMIN';

  return (
    <div
      ref={contentRef}
      className={cn(
        "p-4 rounded-lg w-max min-w-[20%] max-w-[70%] transition-all duration-200",
        isUser
          ? "ml-auto bg-black/5 dark:bg-white/5"
          : "mr-auto bg-blue-500/10",
        isStreaming && "animate-pulse-subtle",
        className
      )}
    >
      {/* Sender header */}
      {sender?.role === 'ADMIN' && (
        <h1 className="text-lg font-semibold pb-2 text-blue-600 dark:text-blue-400">
          AI Assistant
        </h1>
      )}
      {sender?.role === 'USER' && (
        <h1 className="text-lg font-semibold pb-2 text-gray-700 dark:text-gray-300">
          {sender?.name || 'You'}
        </h1>
      )}

      {/* Message content */}
      <div className="relative">
        <div className="whitespace-pre-wrap break-words">
          {displayedContent}
          {showCursor && showTypingCursor && (
            <span className="inline-block w-0.5 h-5 bg-blue-500 ml-1 animate-blink" />
          )}
        </div>
        
        {/* Show typing indicator if streaming but no content yet */}
        {isStreaming && !displayedContent && (
          <div className="flex items-center space-x-2">
            <TypingIndicator size="sm" variant="dots" />
            <span className="text-xs text-gray-500">Generating response...</span>
          </div>
        )}
      </div>

      {/* Streaming status indicator */}
      {isStreaming && displayedContent && (
        <div className="mt-2 flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-xs text-gray-500">Streaming...</span>
        </div>
      )}
    </div>
  );
}

// Hook for managing streaming message state
export function useStreamingMessage(initialContent = '') {
  const [content, setContent] = useState(initialContent);
  const [isStreaming, setIsStreaming] = useState(false);

  const startStreaming = () => {
    setIsStreaming(true);
    setContent('');
  };

  const appendContent = (newContent: string) => {
    setContent(prev => prev + newContent);
  };

  const setFullContent = (fullContent: string) => {
    setContent(fullContent);
  };

  const stopStreaming = () => {
    setIsStreaming(false);
  };

  const reset = () => {
    setContent('');
    setIsStreaming(false);
  };

  return {
    content,
    isStreaming,
    startStreaming,
    appendContent,
    setFullContent,
    stopStreaming,
    reset
  };
}
