"use client";

import React, { create<PERSON>ontext, useContext, useReducer, useCallback, useEffect } from 'react';
import { Message, CombinedChat } from '@/types';
import { getChatMessages } from '@/actions/chat-actions';

// Types for the chat context
interface ChatState {
  chats: CombinedChat[];
  currentChatId: string | null;
  messages: { [chatId: string]: Message[] };
  loadingChats: Set<string>;
  isLoading: boolean;
  error: string | null;
  // Streaming state
  isStreaming: boolean;
  streamingMessageId: string | null;
  streamingChatId: string | null;
}

type ChatAction =
  | { type: 'SET_CHATS'; payload: CombinedChat[] }
  | { type: 'SET_CURRENT_CHAT'; payload: string | null }
  | { type: 'SET_MESSAGES'; payload: { chatId: string; messages: Message[] } }
  | { type: 'ADD_MESSAGE'; payload: { chatId: string; message: Message } }
  | { type: 'UPDATE_MESSAGE'; payload: { chatId: string; messageId: string; content: string } }
  | { type: 'SET_LOADING_CHAT'; payload: { chatId: string; loading: boolean } }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'ADD_CHAT'; payload: CombinedChat }
  | { type: 'REMOVE_CHAT'; payload: string }
  | { type: 'SET_STREAMING'; payload: { isStreaming: boolean; messageId: string | null; chatId: string | null } };

const initialState: ChatState = {
  chats: [],
  currentChatId: null,
  messages: {},
  loadingChats: new Set(),
  isLoading: false,
  error: null,
  isStreaming: false,
  streamingMessageId: null,
  streamingChatId: null,
};

function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_CHATS':
      return { ...state, chats: action.payload };
    
    case 'SET_CURRENT_CHAT':
      return { ...state, currentChatId: action.payload };
    
    case 'SET_MESSAGES':
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.chatId]: action.payload.messages,
        },
      };
    
    case 'ADD_MESSAGE':
      const currentMessages = state.messages[action.payload.chatId] || [];
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.chatId]: [...currentMessages, action.payload.message],
        },
      };
    
    case 'UPDATE_MESSAGE':
      const chatMessages = state.messages[action.payload.chatId] || [];
      const updatedMessages = chatMessages.map(msg =>
        msg.id === action.payload.messageId
          ? { ...msg, content: action.payload.content }
          : msg
      );
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.chatId]: updatedMessages,
        },
      };
    
    case 'SET_LOADING_CHAT':
      const newLoadingChats = new Set(state.loadingChats);
      if (action.payload.loading) {
        newLoadingChats.add(action.payload.chatId);
      } else {
        newLoadingChats.delete(action.payload.chatId);
      }
      return { ...state, loadingChats: newLoadingChats };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'ADD_CHAT':
      return { ...state, chats: [action.payload, ...state.chats] };
    
    case 'REMOVE_CHAT':
      const filteredChats = state.chats.filter(chat => chat.id !== action.payload);
      const { [action.payload]: removed, ...remainingMessages } = state.messages;
      return {
        ...state,
        chats: filteredChats,
        messages: remainingMessages,
        currentChatId: state.currentChatId === action.payload ? null : state.currentChatId,
      };

    case 'SET_STREAMING':
      return {
        ...state,
        isStreaming: action.payload.isStreaming,
        streamingMessageId: action.payload.messageId,
        streamingChatId: action.payload.chatId,
      };

    default:
      return state;
  }
}

interface ChatContextType {
  state: ChatState;
  setChats: (chats: CombinedChat[]) => void;
  setCurrentChat: (chatId: string | null) => void;
  loadChatMessages: (chatId: string) => Promise<void>;
  addMessage: (chatId: string, message: Message) => void;
  updateMessage: (chatId: string, messageId: string, content: string) => void;
  addChat: (chat: CombinedChat) => void;
  removeChat: (chatId: string) => void;
  preloadChat: (chatId: string) => Promise<void>;
  getCachedMessages: (chatId: string) => Message[] | null;
  isMessagesCached: (chatId: string) => boolean;
  setStreaming: (isStreaming: boolean, messageId: string | null, chatId: string | null) => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export function ChatProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(chatReducer, initialState);

  const setChats = useCallback((chats: CombinedChat[]) => {
    dispatch({ type: 'SET_CHATS', payload: chats });
  }, []);

  const setCurrentChat = useCallback((chatId: string | null) => {
    dispatch({ type: 'SET_CURRENT_CHAT', payload: chatId });
  }, []);

  const loadChatMessages = useCallback(async (chatId: string) => {
    // Check if messages are already cached
    if (state.messages[chatId]) {
      return;
    }

    dispatch({ type: 'SET_LOADING_CHAT', payload: { chatId, loading: true } });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const chatMessages = await getChatMessages(chatId);
      const formattedMessages: Message[] = chatMessages.map(msg => ({
        id: `${msg.createdAt.getTime()}-${Math.random()}`,
        role: msg.type === "USER" ? "user" : "assistant",
        content: msg.content,
        sender: msg.sender
          ? {
              id: msg.sender.id,
              role: msg.sender.role === "USER" ? "USER" : "ADMIN",
              name: msg.sender.name || undefined,
              email: msg.sender.email || undefined
            }
          : undefined,
        createdAt: msg.createdAt
      }));

      dispatch({ type: 'SET_MESSAGES', payload: { chatId, messages: formattedMessages } });
    } catch (error) {
      console.error('Failed to load chat messages:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load chat messages' });
    } finally {
      dispatch({ type: 'SET_LOADING_CHAT', payload: { chatId, loading: false } });
    }
  }, [state.messages]);

  const addMessage = useCallback((chatId: string, message: Message) => {
    dispatch({ type: 'ADD_MESSAGE', payload: { chatId, message } });
  }, []);

  const updateMessage = useCallback((chatId: string, messageId: string, content: string) => {
    dispatch({ type: 'UPDATE_MESSAGE', payload: { chatId, messageId, content } });
  }, []);

  const addChat = useCallback((chat: CombinedChat) => {
    dispatch({ type: 'ADD_CHAT', payload: chat });
  }, []);

  const removeChat = useCallback((chatId: string) => {
    dispatch({ type: 'REMOVE_CHAT', payload: chatId });
  }, []);

  const preloadChat = useCallback(async (chatId: string) => {
    if (!state.messages[chatId] && !state.loadingChats.has(chatId)) {
      await loadChatMessages(chatId);
    }
  }, [loadChatMessages, state.messages, state.loadingChats]);

  const getCachedMessages = useCallback((chatId: string): Message[] | null => {
    return state.messages[chatId] || null;
  }, [state.messages]);

  const isMessagesCached = useCallback((chatId: string): boolean => {
    return !!state.messages[chatId];
  }, [state.messages]);

  const setStreaming = useCallback((isStreaming: boolean, messageId: string | null, chatId: string | null) => {
    dispatch({ type: 'SET_STREAMING', payload: { isStreaming, messageId, chatId } });
  }, []);

  const contextValue: ChatContextType = {
    state,
    setChats,
    setCurrentChat,
    loadChatMessages,
    addMessage,
    updateMessage,
    addChat,
    removeChat,
    preloadChat,
    getCachedMessages,
    isMessagesCached,
    setStreaming,
  };

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
}

export function useChatContext() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
}
