"use client";

import { deleteChat } from "@/actions/chat-actions";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useChatContext } from "@/contexts/ChatContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { SidebarMenuAction } from "../ui/sidebar";
import { ShareChatForm } from "./ShareChatForm";

export function ChatActions({ chatId }: { chatId: string }) {
  const router = useRouter();
  const { removeChat, setCurrentChat, state } = useChatContext();

  const handleDelete = async () => {
    // Store the chat data for potential rollback
    const chatToDelete = state.chats.find(chat => chat.id === chatId);

    try {
      // Remove from context immediately for optimistic UI update
      removeChat(chatId);

      // If this was the current chat, navigate away
      if (state.currentChatId === chatId) {
        setCurrentChat(null);
        router.push('/chat');
      }

      // Delete from database
      await deleteChat(chatId);
      toast.success("Chat deleted successfully");
    } catch (error) {
      toast.error("Failed to delete chat");
      console.error(error);

      // Revert optimistic update on error
      if (chatToDelete) {
        // Re-add the chat to the context
        // Note: This is a simple rollback. In a production app, you might want to refetch all chats
        // to ensure consistency with the server state
        window.location.reload(); // Simple fallback for now
      }
    }
  };


  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarMenuAction>
          <MoreHorizontal />
        </SidebarMenuAction>
      </DropdownMenuTrigger>
      <DropdownMenuContent side="right" align="start">
          <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
            <ShareChatForm chatId={chatId} />
          </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDelete}>
          <span>Delete Chat</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}