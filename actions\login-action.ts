"use server";
import * as z from "zod";
import { LoginSchema } from "@/schemas";
import bcrypt from "bcryptjs";
import { signIn } from "@/auth";
import prisma from "@/prisma";

export const login = async (values: any) => {
  const validatedFields = LoginSchema.safeParse(values);
  if (!validatedFields.success) {
    return {
      error: "Invalid credentials",
    };
  }

  const { email, password } = validatedFields.data;
  
  // Find the user by email
  const existingUser = await prisma.user.findUnique({
    where: { email },
  });

  // Return error if user doesn't exist
  if (!existingUser || !existingUser.password) {
    return {
      error: "Invalid credentials",
    };
  }

  // Compare provided password with stored hashed password
  const passwordMatch = await bcrypt.compare(
    password,
    existingUser.password
  );

  // Return error if password doesn't match
  if (!passwordMatch) {
    return {
      error: "Invalid credentials",
    };
  }
  
  await signIn("credentials", validatedFields.data)
};