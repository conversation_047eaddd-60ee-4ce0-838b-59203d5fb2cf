"use client";
import { useState } from "react";
import { shareChat } from "@/actions/chat-actions";
import { toast } from "sonner";

export function ShareChatForm({ chatId }: { chatId: string }) {
  const [userIdsInput, setUserIdsInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleShare = async () => {
    if (!userIdsInput.trim()) return;
    
    setIsSubmitting(true);
    try {
      const userIds = userIdsInput.split(',')
        .map(id => id.trim())
        .filter(id => id.length > 0);

      const result = await shareChat(chatId, userIds);

      toast.success(
        `Shared with ${result.successCount} users. ` +
        `${result.invalidIds.length} invalid IDs.`
      );
      
      setUserIdsInput("");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to share chat");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4 p-4">
      <div className="space-y-2">
        <label className="block text-sm font-medium">
          Enter User IDs (comma-separated)
        </label>
        <input
          type="text"
          value={userIdsInput}
          onChange={(e) => setUserIdsInput(e.target.value)}
          placeholder="clp123abc, clp456def, clp789ghi"
          className="w-full p-2 border rounded"
          disabled={isSubmitting}
        />
      </div>

      <button
        onClick={handleShare}
        disabled={isSubmitting || !userIdsInput.trim()}
        className="w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {isSubmitting ? "Sharing..." : "Share Chat"}
      </button>
    </div>
  );
}